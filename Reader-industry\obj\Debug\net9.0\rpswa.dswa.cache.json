{"GlobalPropertiesHash": "fvvoNBaI2H42jh01CXb2NMbDegfWbUwNnUMT/TeMyWM=", "FingerprintPatternsHash": "gq3WsqcKBUGTSNle7RKKyXRIwh7M8ccEqOqYvIzoM04=", "PropertyOverridesHash": "8ZRc1sGeVrPBx4lD717BgRaQekyh78QKV9SKsdt638U=", "InputHashes": ["07GMfchoMcd7e9yHpaz6iaS6RG6/FUYy7WHuLZ4TuG4=", "ejm+MqiOkCdURq9y1it2GiRtc8xlZvt56RT3gDcYST8=", "lfnjUvo2f0u8uxm/0/6EFmrd0mIl1I3kAFpEhgPwngA=", "wTtFTMtLrsOgMJALMnFEkG1C972kR0S4lIHPLwiwlUI=", "KYV0yMnd1BAha1UXnfNQ04S0+eZD/EKeFMuofUr6wgE=", "zGAhDcXdD+ejfD+Tsqviv3swy+dgUex482i62N2884E=", "N+k/e8uzHdmqTTv/HEx1kaGl5FIyAxlvtxr0V6aT0L8=", "2J1QHAjjJSb+2LdqppbllgYyVoig8pl6x21onVYhKWg=", "OvZF2zQYFMuYehtRHyDdeDVS6dn1zz2E6utOQcjO/ug=", "+pbssFjdG9sZs4qVn6lTuBC3PthrkLU1Wkj/S7B2498=", "6JzjNWB3WXotcPXYH6uHXIM180S8FR4XSRE90HSp0jM=", "/re7/nLb4b5PoA/Wcp8t5JXP1PMWPxMD5F25qmKFMwM=", "tQviX/JyabdpeM3hYeZUyCqX8XjXfFHOGGjinDbsiEc=", "qPJ5b+tvusZfC6erkzRYcNpDXXNnT6QtUDzd1mWw75E=", "2F6Cl5BOYtTKo/LPFOPK9ps/dLcBSzZwOyIOOVGJH7A=", "uuBJ7FKUo+gfxAky5cwbrhA91vbqhJfJhoIRduuSlAQ=", "BRYH2pA3Me43jOQDL4vtFXl+WDtBxJEAE1eDdpse9qQ=", "dm/BP6+MAWDq8wus7slx0BvInrV6yNUCD9ylZTHyhy0=", "nEHQl5m73BFpQL/CQVDMiRIlif3DvHjeOQN6QT3Da4o=", "KgI+lxAVSq9lnAXQuAT4SMR653E9SBo6u1xjhg2jOWE=", "32b/osWD9j2YbI3hkGFt3s9GMBDQ2FFm+xgf3i6MuCs=", "UfF1AUYj3O+XBbkZ2Mf9kpF7JM7PiBMgkoHxblB5cmY=", "aarEkko1HcO4Q6tvMRKIXhgOYhMsaWthXNWkU3HTgNk=", "JDNR9wBg8ML/a0Bq1keIlGUwVIlQkEIE5DpqFGYSG7Y=", "WJbmVBrD1JwdiA+CDIcn3mRh4WaByQvKyd6SIY7ih3Q=", "fTgRZ+Ceys4YbcKUIIBRvVgUZCc5STJWK/RKZeQcENE=", "aoBx1kmpgKpQ6HVoF77IHFqa6f5XGbmrSVyfDztZzYc=", "ER0vb8CraEnDrc5NIofbt0Hz1VUzOO/DRBDtGcxR8xQ=", "s7hTktHgcB5iWNsxf14ES+af+nv8I3ZUsJo4H8WOvDs=", "UkRaTlropXO7fb4FlFcEhgBWgBl+J9xg6p4a7N7b5UQ=", "T7KIsjd1UTlYBj3thXLKSCehLHlHfFp3frGAZtzZYy4=", "7E72oQmaeZyKWlCQaeXeN/V3WEUgM/nBFuhMLGcWFbU=", "8LTDv7zOlChjQkW3EUng8bvi5DWH8iqyKEgbFHkLqXA=", "+TgnCiU3UDQLEuFJ0n5qzv7S3eT5vzJdmIIMsiUEins=", "lgNZqTAFgBqQHnNAfoYx7vyU/e3jffWMA3QM3GbBLjk=", "ps3JHbZQ88qLWogOvAw3LcNuXtt2x7Prs2oJCwf+GFM=", "8v7huDaKhrAiLt4Zfb2l3CaBe+z9VcdOnm6kRpma4y0=", "KQu1YqO3Gk78RTo7t3BmWhxIARCthhqVLy1OaImCrdk=", "+WK3XxyzDAtyVrp/2flmNHgBlLrAUuM8MC78sgyG/bU=", "lXWX3XU6EmiuO1rmjgTKSYwj8jfHjkeDHsmLU7NeUs4=", "w7OgAtaY1qtIdkFc+uMH9oxWOyT5wuVp5EgXwiM8eoA=", "qhqbactQFhqiPDboXglvFWxYp9ygM5V6gQRkDks7HzI=", "NJyvdZNHbPRcPyhF5q9w8eXxg8In5qyPPRDVHrsfNpY=", "2oOtmFzqYPAUzmkxNs8WUx/0ao2WJCHtEeOeiL+NJlU=", "RXKmGIbyAbC/EYZmSwWzitJdrt77HAtB2Mti+7SWOt4=", "62ZU9ky9P8IKP+HFFxdBby21Ps69FRJ83G7LgSJPc14=", "L7QxRJrmlG0fSFWcpvwq4U6hadzPEqwhjcCrGcFRwKg=", "FyGOUuk7V5s606ATxGz8w3hiBvROcDoE6+PiqamQazs=", "1vNwvBPkHdCqgmMQz4Ucg9FakrAcsT/FqLQZ+qwzx+M=", "nBulAVXvoE1ks99KlZbKjSjEF+tGl57TqvL3qcJl33s=", "z1IehXMCDljGhyZcZLpKag67432dqms7DuRJ9WAfy9Y=", "cPRceijtPwnnlcEwXY78xCnjjXilFqB6XwoWlcBOlsI=", "Q+RAKG5J5lZXgoQZtX0c5hbYe+M9F1MCw/lvdID+CBQ=", "VjWZ9BcjuQRaF05DIYh2Deo2u3Tv+O2bVBAYysGJqJA=", "b4cCSuBylJJTE+uoYoKqlKdjIJPyXeiEnx0+TO0tHok=", "vUau+he+Xvd86VS12eA1r0pzMW9HHrjwIgf6qbuhKxc=", "WN3hBBcSRWJ2y16WmnHXg+6qLTchkF6TENVKZn2pTMg=", "X+Az8LghF4jtJckiF57wRpb9LREsXy41gjoZFVv0Zs4=", "diq2U28lSU5q6zE4G4+8L/fhTsyQKGzVzfYzkaybUDY=", "P+S3R3yPGrhYrOwlym7JT/gqrTtml6KeQiDWGYphT/M=", "hlBw/96XLsOabofwwRN168uhAEXfKUDwhqZT1oDtpwY=", "vGB4tmY6Z3Hm9/lFVXSxQh+wnBjT4Tkoafi9kLpj3+o=", "A92n7YBsyG9Wx9NleYMQQndL3K7zY3FYECMt9CAeKVs=", "KB9nvVgXvEfMA7qA67Y5wzS5hqIVyl05HEG1n3jqBUA=", "1qeEetgbVdsHDEzkzOxQob3I+PvBmW35ygOmvwTdzG4=", "6TY1jjYQTuj1+jTcLzCZTGqPMXQUH3l1g0IbOn7Xgj0=", "2D6hlrlug+7s6RfAMFxwfEmpT6PXl3cypM579vLxE0Q=", "2KmEc/txqF4+YIv4F7ZgQ8kSxocmKxSuIdxvqcPwCl0=", "mb0zMJxSsobYn25tAhC7mcaPecDOFF3A+iGLU0Aa38k=", "8myrOHNFahWQbenxIO4TSUnQYglujrA3UrCvak25AZY=", "XCByrN+Z1MKYGwV/AcZzOT9w+0vTSO/N0v0PAbNTZ5g=", "HNKfMyJsLrY9o05KcXMqeD/MD9JzgQb8VNcVGzX+xLw=", "yLiZwYdtqnedbZ++o3WR8xk1CnU5gQ8u8SOGeqEOJIc="], "CachedAssets": {"07GMfchoMcd7e9yHpaz6iaS6RG6/FUYy7WHuLZ4TuG4=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\Reader-industry\\Reader-industry\\wwwroot\\css\\site.css", "SourceId": "Reader-industry", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\Reader-industry\\Reader-industry\\wwwroot\\", "BasePath": "_content/Reader-industry", "RelativePath": "css/site#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "b9sayid5wm", "Integrity": "j6fhJSuuyLpOSLuPJU0TsDV0iNjor5S3rDnvxJrt4bg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\css\\site.css", "FileLength": 667, "LastWriteTime": "2025-07-09T14:58:03.6025092+00:00"}, "ejm+MqiOkCdURq9y1it2GiRtc8xlZvt56RT3gDcYST8=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\Reader-industry\\Reader-industry\\wwwroot\\favicon.ico", "SourceId": "Reader-industry", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\Reader-industry\\Reader-industry\\wwwroot\\", "BasePath": "_content/Reader-industry", "RelativePath": "favicon#[.{fingerprint}]?.ico", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "61n19gt1b8", "Integrity": "Jtxf9L+5ITKRc1gIRl4VbUpGkRNfOBXjYTdhJD4facM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\favicon.ico", "FileLength": 5430, "LastWriteTime": "2025-07-09T14:58:03.378322+00:00"}, "lfnjUvo2f0u8uxm/0/6EFmrd0mIl1I3kAFpEhgPwngA=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\Reader-industry\\Reader-industry\\wwwroot\\js\\site.js", "SourceId": "Reader-industry", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\Reader-industry\\Reader-industry\\wwwroot\\", "BasePath": "_content/Reader-industry", "RelativePath": "js/site#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "xtxxf3hu2r", "Integrity": "hRQyftXiu1lLX2P9Ly9xa4gHJgLeR1uGN5qegUobtGo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\js\\site.js", "FileLength": 231, "LastWriteTime": "2025-07-09T14:58:03.6035075+00:00"}, "wTtFTMtLrsOgMJALMnFEkG1C972kR0S4lIHPLwiwlUI=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\Reader-industry\\Reader-industry\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css", "SourceId": "Reader-industry", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\Reader-industry\\Reader-industry\\wwwroot\\", "BasePath": "_content/Reader-industry", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "bqjiyaj88i", "Integrity": "Yy5/hBqRmmU2MJ1TKwP2aXoTO6+OjzrLmJIsC2Wy4H8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css", "FileLength": 70329, "LastWriteTime": "2025-07-09T14:58:03.1613506+00:00"}, "KYV0yMnd1BAha1UXnfNQ04S0+eZD/EKeFMuofUr6wgE=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\Reader-industry\\Reader-industry\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css.map", "SourceId": "Reader-industry", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\Reader-industry\\Reader-industry\\wwwroot\\", "BasePath": "_content/Reader-industry", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "c2j<PERSON><PERSON><PERSON><PERSON>", "Integrity": "xAT+n25FE5hvOjj2fG4YdOwr1bl4IlAJBNg6PbhLT2E=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css.map", "FileLength": 203221, "LastWriteTime": "2025-07-09T14:58:03.1633468+00:00"}, "zGAhDcXdD+ejfD+Tsqviv3swy+dgUex482i62N2884E=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\Reader-industry\\Reader-industry\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css", "SourceId": "Reader-industry", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\Reader-industry\\Reader-industry\\wwwroot\\", "BasePath": "_content/Reader-industry", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "erw9l3u2r3", "Integrity": "5nDHMGiyfZHl3UXePuhLDQR9ncPfBR1HJeZLXyJNV24=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css", "FileLength": 51795, "LastWriteTime": "2025-07-09T14:58:03.1653774+00:00"}, "N+k/e8uzHdmqTTv/HEx1kaGl5FIyAxlvtxr0V6aT0L8=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\Reader-industry\\Reader-industry\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css.map", "SourceId": "Reader-industry", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\Reader-industry\\Reader-industry\\wwwroot\\", "BasePath": "_content/Reader-industry", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "aexeepp0ev", "Integrity": "kgL+xwVmM8IOs15lnoHt9daR2LRMiBG/cYgUPcKQOY4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css.map", "FileLength": 115986, "LastWriteTime": "2025-07-09T14:58:03.1664262+00:00"}, "2J1QHAjjJSb+2LdqppbllgYyVoig8pl6x21onVYhKWg=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\Reader-industry\\Reader-industry\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css", "SourceId": "Reader-industry", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\Reader-industry\\Reader-industry\\wwwroot\\", "BasePath": "_content/Reader-industry", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "d7shbmvgxk", "Integrity": "CZxoF8zjaLlyVkcvVCDlc8CeQR1w1RMrvgYx30cs8kM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css", "FileLength": 70403, "LastWriteTime": "2025-07-09T14:58:03.1676737+00:00"}, "OvZF2zQYFMuYehtRHyDdeDVS6dn1zz2E6utOQcjO/ug=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\Reader-industry\\Reader-industry\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css.map", "SourceId": "Reader-industry", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\Reader-industry\\Reader-industry\\wwwroot\\", "BasePath": "_content/Reader-industry", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "ausgxo2sd3", "Integrity": "/siQUA8yX830j+cL4amKHY3yBtn3n8z3Eg+VZ15f90k=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css.map", "FileLength": 203225, "LastWriteTime": "2025-07-09T14:58:03.1691726+00:00"}, "+pbssFjdG9sZs4qVn6lTuBC3PthrkLU1Wkj/S7B2498=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\Reader-industry\\Reader-industry\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css", "SourceId": "Reader-industry", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\Reader-industry\\Reader-industry\\wwwroot\\", "BasePath": "_content/Reader-industry", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "k8d9w2qqmf", "Integrity": "vMxTcvkC4Ly7LiAT3G8yEy9EpTr7Fge4SczWp07/p3k=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css", "FileLength": 51870, "LastWriteTime": "2025-07-09T14:58:03.1701728+00:00"}, "6JzjNWB3WXotcPXYH6uHXIM180S8FR4XSRE90HSp0jM=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\Reader-industry\\Reader-industry\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css.map", "SourceId": "Reader-industry", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\Reader-industry\\Reader-industry\\wwwroot\\", "BasePath": "_content/Reader-industry", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "cosvhxvwiu", "Integrity": "7GdOlw7U/wgyaeUtFmxPz5/MphdvVSPtVOOlTn9c33Q=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css.map", "FileLength": 116063, "LastWriteTime": "2025-07-09T14:58:03.1711726+00:00"}, "/re7/nLb4b5PoA/Wcp8t5JXP1PMWPxMD5F25qmKFMwM=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\Reader-industry\\Reader-industry\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css", "SourceId": "Reader-industry", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\Reader-industry\\Reader-industry\\wwwroot\\", "BasePath": "_content/Reader-industry", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "ub07r2b239", "Integrity": "lo9YI82OF03vojdu+XOR3+DRrLIpMhpzZNmHbM5CDMA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css", "FileLength": 12065, "LastWriteTime": "2025-07-09T14:58:03.1711726+00:00"}, "tQviX/JyabdpeM3hYeZUyCqX8XjXfFHOGGjinDbsiEc=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\Reader-industry\\Reader-industry\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css.map", "SourceId": "Reader-industry", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\Reader-industry\\Reader-industry\\wwwroot\\", "BasePath": "_content/Reader-industry", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "fvhpjtyr6v", "Integrity": "RXJ/QZiBfHXoPtXR2EgC+bFo2pe3GtbZO722RtiLGzQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css.map", "FileLength": 129371, "LastWriteTime": "2025-07-09T14:58:03.1731717+00:00"}, "qPJ5b+tvusZfC6erkzRYcNpDXXNnT6QtUDzd1mWw75E=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\Reader-industry\\Reader-industry\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css", "SourceId": "Reader-industry", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\Reader-industry\\Reader-industry\\wwwroot\\", "BasePath": "_content/Reader-industry", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "b7pk76d08c", "Integrity": "l8vt5oozv958eMd9TFsPAWgl9JJK9YKfbVSs8mchQ84=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css", "FileLength": 10126, "LastWriteTime": "2025-07-09T14:58:03.1731717+00:00"}, "2F6Cl5BOYtTKo/LPFOPK9ps/dLcBSzZwOyIOOVGJH7A=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\Reader-industry\\Reader-industry\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css.map", "SourceId": "Reader-industry", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\Reader-industry\\Reader-industry\\wwwroot\\", "BasePath": "_content/Reader-industry", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "fsbi9cje9m", "Integrity": "0eqVT62kqRLJh9oTqLeIH4UnQskqVjib8hl2fXxl4lg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css.map", "FileLength": 51369, "LastWriteTime": "2025-07-09T14:58:03.1741765+00:00"}, "uuBJ7FKUo+gfxAky5cwbrhA91vbqhJfJhoIRduuSlAQ=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\Reader-industry\\Reader-industry\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css", "SourceId": "Reader-industry", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\Reader-industry\\Reader-industry\\wwwroot\\", "BasePath": "_content/Reader-industry", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "rzd6atqjts", "Integrity": "V8psnHoJS/MPlCXWwc/J3tGtp9c3gGFRmqsIQgpn+Gg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css", "FileLength": 12058, "LastWriteTime": "2025-07-09T14:58:03.175256+00:00"}, "BRYH2pA3Me43jOQDL4vtFXl+WDtBxJEAE1eDdpse9qQ=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\Reader-industry\\Reader-industry\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css.map", "SourceId": "Reader-industry", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\Reader-industry\\Reader-industry\\wwwroot\\", "BasePath": "_content/Reader-industry", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "ee0r1s7dh0", "Integrity": "OoQVwh7Arp7bVoK2ZiTx2S//KrnPrSPzPZ93CqCMhe8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css.map", "FileLength": 129386, "LastWriteTime": "2025-07-09T14:58:03.1762557+00:00"}, "dm/BP6+MAWDq8wus7slx0BvInrV6yNUCD9ylZTHyhy0=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\Reader-industry\\Reader-industry\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css", "SourceId": "Reader-industry", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\Reader-industry\\Reader-industry\\wwwroot\\", "BasePath": "_content/Reader-industry", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "dxx9fxp4il", "Integrity": "/8jh8hcEMFKyS6goWqnNu7t3EzZPCGdQZgO6sCkI8tI=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css", "FileLength": 10198, "LastWriteTime": "2025-07-09T14:58:03.1772572+00:00"}, "nEHQl5m73BFpQL/CQVDMiRIlif3DvHjeOQN6QT3Da4o=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\Reader-industry\\Reader-industry\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css.map", "SourceId": "Reader-industry", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\Reader-industry\\Reader-industry\\wwwroot\\", "BasePath": "_content/Reader-industry", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "jd9uben2k1", "Integrity": "910zw+rMdcg0Ls48ATp65vEn8rd5HvPxOKm2x3/CBII=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css.map", "FileLength": 63943, "LastWriteTime": "2025-07-09T14:58:03.178257+00:00"}, "KgI+lxAVSq9lnAXQuAT4SMR653E9SBo6u1xjhg2jOWE=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\Reader-industry\\Reader-industry\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css", "SourceId": "Reader-industry", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\Reader-industry\\Reader-industry\\wwwroot\\", "BasePath": "_content/Reader-industry", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "khv3u5hwcm", "Integrity": "2BubgNUPlQSF/0wLFcRXQ/Yjzk9vsUbDAeK2QM+h+yo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css", "FileLength": 107823, "LastWriteTime": "2025-07-09T14:58:03.1792604+00:00"}, "32b/osWD9j2YbI3hkGFt3s9GMBDQ2FFm+xgf3i6MuCs=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\Reader-industry\\Reader-industry\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css.map", "SourceId": "Reader-industry", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\Reader-industry\\Reader-industry\\wwwroot\\", "BasePath": "_content/Reader-industry", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "r4e9w2rdcm", "Integrity": "Nfjrc4Ur9Fv2oBEswQWIyBnNDP99q+LhL+z9553O0cY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css.map", "FileLength": 267535, "LastWriteTime": "2025-07-09T14:58:03.1824477+00:00"}, "UfF1AUYj3O+XBbkZ2Mf9kpF7JM7PiBMgkoHxblB5cmY=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\Reader-industry\\Reader-industry\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css", "SourceId": "Reader-industry", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\Reader-industry\\Reader-industry\\wwwroot\\", "BasePath": "_content/Reader-industry", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "lcd1t2u6c8", "Integrity": "KyE9xbKO9CuYx0HXpIKgsWIvXkAfITtiQ172j26wmRs=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css", "FileLength": 85352, "LastWriteTime": "2025-07-09T14:58:03.1834486+00:00"}, "aarEkko1HcO4Q6tvMRKIXhgOYhMsaWthXNWkU3HTgNk=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\Reader-industry\\Reader-industry\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css.map", "SourceId": "Reader-industry", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\Reader-industry\\Reader-industry\\wwwroot\\", "BasePath": "_content/Reader-industry", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "c2oey78nd0", "Integrity": "rHDmip4JZzuaGOcSQ1QSQrIbG0Eb3Zja9whqSF1zYIU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css.map", "FileLength": 180381, "LastWriteTime": "2025-07-09T14:58:03.1844487+00:00"}, "JDNR9wBg8ML/a0Bq1keIlGUwVIlQkEIE5DpqFGYSG7Y=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\Reader-industry\\Reader-industry\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css", "SourceId": "Reader-industry", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\Reader-industry\\Reader-industry\\wwwroot\\", "BasePath": "_content/Reader-industry", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "tdbxkamptv", "Integrity": "H6wkBbSwjua2veJoThJo4uy161jp+DOiZTloUlcZ6qQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css", "FileLength": 107691, "LastWriteTime": "2025-07-09T14:58:03.1854485+00:00"}, "WJbmVBrD1JwdiA+CDIcn3mRh4WaByQvKyd6SIY7ih3Q=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\Reader-industry\\Reader-industry\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css.map", "SourceId": "Reader-industry", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\Reader-industry\\Reader-industry\\wwwroot\\", "BasePath": "_content/Reader-industry", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "j5mq2jizvt", "Integrity": "p0BVq5Ve/dohBIdfbrZsoQNu02JSsKh1g0wbyiQiUaU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css.map", "FileLength": 267476, "LastWriteTime": "2025-07-09T14:58:03.1864488+00:00"}, "fTgRZ+Ceys4YbcKUIIBRvVgUZCc5STJWK/RKZeQcENE=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\Reader-industry\\Reader-industry\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css", "SourceId": "Reader-industry", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\Reader-industry\\Reader-industry\\wwwroot\\", "BasePath": "_content/Reader-industry", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "06098lyss8", "Integrity": "GAUum6FjwQ8HrXGaoFRnHTqQQLpljXGavT7mBX8E9qU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css", "FileLength": 85281, "LastWriteTime": "2025-07-09T14:58:03.1874473+00:00"}, "aoBx1kmpgKpQ6HVoF77IHFqa6f5XGbmrSVyfDztZzYc=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\Reader-industry\\Reader-industry\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css.map", "SourceId": "Reader-industry", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\Reader-industry\\Reader-industry\\wwwroot\\", "BasePath": "_content/Reader-industry", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "nvvlpmu67g", "Integrity": "o8XK32mcY/FfcOQ1D2HJvVuZ0YTXSURZDLXCK0fnQeA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css.map", "FileLength": 180217, "LastWriteTime": "2025-07-09T14:58:03.1884457+00:00"}, "ER0vb8CraEnDrc5NIofbt0Hz1VUzOO/DRBDtGcxR8xQ=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\Reader-industry\\Reader-industry\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css", "SourceId": "Reader-industry", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\Reader-industry\\Reader-industry\\wwwroot\\", "BasePath": "_content/Reader-industry", "RelativePath": "lib/bootstrap/dist/css/bootstrap#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "s35ty4nyc5", "Integrity": "GKEF18s44B5e0MolXAkpkqLiEbOVlKf6VyYr/G/E6pw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css", "FileLength": 281046, "LastWriteTime": "2025-07-09T14:58:03.1894453+00:00"}, "s7hTktHgcB5iWNsxf14ES+af+nv8I3ZUsJo4H8WOvDs=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\Reader-industry\\Reader-industry\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css.map", "SourceId": "Reader-industry", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\Reader-industry\\Reader-industry\\wwwroot\\", "BasePath": "_content/Reader-industry", "RelativePath": "lib/bootstrap/dist/css/bootstrap.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "pj5nd1wqec", "Integrity": "KzNVR3p7UZGba94dnCtlc6jXjK5urSPiZ/eNnKTmDkw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css.map", "FileLength": 679755, "LastWriteTime": "2025-07-09T14:58:03.192822+00:00"}, "UkRaTlropXO7fb4FlFcEhgBWgBl+J9xg6p4a7N7b5UQ=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\Reader-industry\\Reader-industry\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css", "SourceId": "Reader-industry", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\Reader-industry\\Reader-industry\\wwwroot\\", "BasePath": "_content/Reader-industry", "RelativePath": "lib/bootstrap/dist/css/bootstrap.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "46ein0sx1k", "Integrity": "PI8n5gCcz9cQqQXm3PEtDuPG8qx9oFsFctPg0S5zb8g=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css", "FileLength": 232803, "LastWriteTime": "2025-07-09T14:58:03.1948205+00:00"}, "T7KIsjd1UTlYBj3thXLKSCehLHlHfFp3frGAZtzZYy4=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\Reader-industry\\Reader-industry\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css.map", "SourceId": "Reader-industry", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\Reader-industry\\Reader-industry\\wwwroot\\", "BasePath": "_content/Reader-industry", "RelativePath": "lib/bootstrap/dist/css/bootstrap.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "v0zj4ognzu", "Integrity": "8SM4U2NQpCLGTQLW5D/x3qSTwxVq2CP+GXYc3V1WwFs=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css.map", "FileLength": 589892, "LastWriteTime": "2025-07-09T14:58:03.1968216+00:00"}, "7E72oQmaeZyKWlCQaeXeN/V3WEUgM/nBFuhMLGcWFbU=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\Reader-industry\\Reader-industry\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css", "SourceId": "Reader-industry", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\Reader-industry\\Reader-industry\\wwwroot\\", "BasePath": "_content/Reader-industry", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "37tfw0ft22", "Integrity": "j5E4XIj1p1kNnDi0x1teX9RXoh1/FNlPvCML9YmRh2Q=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css", "FileLength": 280259, "LastWriteTime": "2025-07-09T14:58:03.1988211+00:00"}, "8LTDv7zOlChjQkW3EUng8bvi5DWH8iqyKEgbFHkLqXA=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\Reader-industry\\Reader-industry\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css.map", "SourceId": "Reader-industry", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\Reader-industry\\Reader-industry\\wwwroot\\", "BasePath": "_content/Reader-industry", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "hrwsygsryq", "Integrity": "3bYWUiiVYMZfv2wq5JnXIsHlQKgSKs/VcRivgjgZ1ho=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css.map", "FileLength": 679615, "LastWriteTime": "2025-07-09T14:58:03.202372+00:00"}, "+TgnCiU3UDQLEuFJ0n5qzv7S3eT5vzJdmIIMsiUEins=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\Reader-industry\\Reader-industry\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css", "SourceId": "Reader-industry", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\Reader-industry\\Reader-industry\\wwwroot\\", "BasePath": "_content/Reader-industry", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "pk9g2wxc8p", "Integrity": "h5lE7Nm8SkeIpBHHYxN99spP3VuGFKl5NZgsocil7zk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css", "FileLength": 232911, "LastWriteTime": "2025-07-09T14:58:03.2043737+00:00"}, "lgNZqTAFgBqQHnNAfoYx7vyU/e3jffWMA3QM3GbBLjk=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\Reader-industry\\Reader-industry\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css.map", "SourceId": "Reader-industry", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\Reader-industry\\Reader-industry\\wwwroot\\", "BasePath": "_content/Reader-industry", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "ft3s53vfgj", "Integrity": "rTzXlnepcb/vgFAiB+U7ODQAfOlJLfM3gY6IU7eIANk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css.map", "FileLength": 589087, "LastWriteTime": "2025-07-09T14:58:03.2083738+00:00"}, "ps3JHbZQ88qLWogOvAw3LcNuXtt2x7Prs2oJCwf+GFM=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\Reader-industry\\Reader-industry\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js", "SourceId": "Reader-industry", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\Reader-industry\\Reader-industry\\wwwroot\\", "BasePath": "_content/Reader-industry", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "6cfz1n2cew", "Integrity": "mkoRoV24jV+rCPWcHDR5awPx8VuzzJKN0ibhxZ9/WaM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js", "FileLength": 207819, "LastWriteTime": "2025-07-09T14:58:03.2106828+00:00"}, "8v7huDaKhrAiLt4Zfb2l3CaBe+z9VcdOnm6kRpma4y0=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\Reader-industry\\Reader-industry\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js.map", "SourceId": "Reader-industry", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\Reader-industry\\Reader-industry\\wwwroot\\", "BasePath": "_content/Reader-industry", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "6pdc2jztkx", "Integrity": "Wq4aWW1rQdJ+6oAgy1JQc9IBjHL9T3MKfXTBNqOv02c=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js.map", "FileLength": 444579, "LastWriteTime": "2025-07-09T14:58:03.2148621+00:00"}, "KQu1YqO3Gk78RTo7t3BmWhxIARCthhqVLy1OaImCrdk=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\Reader-industry\\Reader-industry\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js", "SourceId": "Reader-industry", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\Reader-industry\\Reader-industry\\wwwroot\\", "BasePath": "_content/Reader-industry", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "493y06b0oq", "Integrity": "CDOy6cOibCWEdsRiZuaHf8dSGGJRYuBGC+mjoJimHGw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js", "FileLength": 80721, "LastWriteTime": "2025-07-09T14:58:03.2158735+00:00"}, "+WK3XxyzDAtyVrp/2flmNHgBlLrAUuM8MC78sgyG/bU=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\Reader-industry\\Reader-industry\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js.map", "SourceId": "Reader-industry", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\Reader-industry\\Reader-industry\\wwwroot\\", "BasePath": "_content/Reader-industry", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.min.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "iovd86k7lj", "Integrity": "Xj4HYxZBQ7qqHKBwa2EAugRS+RHWzpcTtI49vgezUSU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js.map", "FileLength": 332090, "LastWriteTime": "2025-07-09T14:58:03.2181828+00:00"}, "lXWX3XU6EmiuO1rmjgTKSYwj8jfHjkeDHsmLU7NeUs4=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\Reader-industry\\Reader-industry\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js", "SourceId": "Reader-industry", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\Reader-industry\\Reader-industry\\wwwroot\\", "BasePath": "_content/Reader-industry", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "vr1egmr9el", "Integrity": "exiXZNJDwucXfuje3CbXPbuS6+Ery3z9sP+pgmvh8nA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js", "FileLength": 135829, "LastWriteTime": "2025-07-09T14:58:03.219197+00:00"}, "w7OgAtaY1qtIdkFc+uMH9oxWOyT5wuVp5EgXwiM8eoA=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\Reader-industry\\Reader-industry\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js.map", "SourceId": "Reader-industry", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\Reader-industry\\Reader-industry\\wwwroot\\", "BasePath": "_content/Reader-industry", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "kbrnm935zg", "Integrity": "EPRLgpqWkahLxEn6CUjdM76RIYIw1xdHwTbeHssuj/4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js.map", "FileLength": 305438, "LastWriteTime": "2025-07-09T14:58:03.2221933+00:00"}, "qhqbactQFhqiPDboXglvFWxYp9ygM5V6gQRkDks7HzI=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\Reader-industry\\Reader-industry\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js", "SourceId": "Reader-industry", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\Reader-industry\\Reader-industry\\wwwroot\\", "BasePath": "_content/Reader-industry", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "jj8uyg4cgr", "Integrity": "QZdFT1ZNdly4rmgUBtXmXFS9BU1FTa+sPe6h794sFRQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js", "FileLength": 73935, "LastWriteTime": "2025-07-09T14:58:03.223193+00:00"}, "NJyvdZNHbPRcPyhF5q9w8eXxg8In5qyPPRDVHrsfNpY=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\Reader-industry\\Reader-industry\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js.map", "SourceId": "Reader-industry", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\Reader-industry\\Reader-industry\\wwwroot\\", "BasePath": "_content/Reader-industry", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.min.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "y7v9cxd14o", "Integrity": "Tsbv8z6VlNgVET8xvz/yLo/v5iJHTAj2J4hkhjP1rHM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js.map", "FileLength": 222455, "LastWriteTime": "2025-07-09T14:58:03.225193+00:00"}, "2oOtmFzqYPAUzmkxNs8WUx/0ao2WJCHtEeOeiL+NJlU=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\Reader-industry\\Reader-industry\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js", "SourceId": "Reader-industry", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\Reader-industry\\Reader-industry\\wwwroot\\", "BasePath": "_content/Reader-industry", "RelativePath": "lib/bootstrap/dist/js/bootstrap#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "notf2xhcfb", "Integrity": "+UW802wgVfnjaSbdwyHLlU7AVplb0WToOlvN1CnzIac=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js", "FileLength": 145401, "LastWriteTime": "2025-07-09T14:58:03.2261924+00:00"}, "RXKmGIbyAbC/EYZmSwWzitJdrt77HAtB2Mti+7SWOt4=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\Reader-industry\\Reader-industry\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js.map", "SourceId": "Reader-industry", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\Reader-industry\\Reader-industry\\wwwroot\\", "BasePath": "_content/Reader-industry", "RelativePath": "lib/bootstrap/dist/js/bootstrap.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "h1s4sie4z3", "Integrity": "9Wr7Hxe8gCJDoIHh5xP29ldXvC3kN2GkifQj9c8vYx4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js.map", "FileLength": 306606, "LastWriteTime": "2025-07-09T14:58:03.2281924+00:00"}, "62ZU9ky9P8IKP+HFFxdBby21Ps69FRJ83G7LgSJPc14=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\Reader-industry\\Reader-industry\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js", "SourceId": "Reader-industry", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\Reader-industry\\Reader-industry\\wwwroot\\", "BasePath": "_content/Reader-industry", "RelativePath": "lib/bootstrap/dist/js/bootstrap.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "63fj8s7r0e", "Integrity": "3gQJhtmj7YnV1fmtbVcnAV6eI4ws0Tr48bVZCThtCGQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js", "FileLength": 60635, "LastWriteTime": "2025-07-09T14:58:03.2291932+00:00"}, "L7QxRJrmlG0fSFWcpvwq4U6hadzPEqwhjcCrGcFRwKg=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\Reader-industry\\Reader-industry\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js.map", "SourceId": "Reader-industry", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\Reader-industry\\Reader-industry\\wwwroot\\", "BasePath": "_content/Reader-industry", "RelativePath": "lib/bootstrap/dist/js/bootstrap.min.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "0j3bgjxly4", "Integrity": "ZI01e/ns473GKvACG4McggJdxvFfFIw4xspwQiG8Ye4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js.map", "FileLength": 220561, "LastWriteTime": "2025-07-09T14:58:03.2301937+00:00"}, "FyGOUuk7V5s606ATxGz8w3hiBvROcDoE6+PiqamQazs=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\Reader-industry\\Reader-industry\\wwwroot\\lib\\bootstrap\\LICENSE", "SourceId": "Reader-industry", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\Reader-industry\\Reader-industry\\wwwroot\\", "BasePath": "_content/Reader-industry", "RelativePath": "lib/bootstrap/LICENSE#[.{fingerprint}]?", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "81b7ukuj9c", "Integrity": "ZH6pA6BSx6fuHZvdaKph1DwUJ+VSYilIiEQu8ilnvqk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\LICENSE", "FileLength": 1153, "LastWriteTime": "2025-07-09T14:58:03.1988211+00:00"}, "1vNwvBPkHdCqgmMQz4Ucg9FakrAcsT/FqLQZ+qwzx+M=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\Reader-industry\\Reader-industry\\wwwroot\\lib\\jquery-validation-unobtrusive\\dist\\jquery.validate.unobtrusive.js", "SourceId": "Reader-industry", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\Reader-industry\\Reader-industry\\wwwroot\\", "BasePath": "_content/Reader-industry", "RelativePath": "lib/jquery-validation-unobtrusive/dist/jquery.validate.unobtrusive#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "47otxtyo56", "Integrity": "wJQaJ0XynBE2fq6CexXXhxKu7fstVmQc7V2MHNTo+WQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation-unobtrusive\\dist\\jquery.validate.unobtrusive.js", "FileLength": 19385, "LastWriteTime": "2025-07-09T14:58:03.5713904+00:00"}, "nBulAVXvoE1ks99KlZbKjSjEF+tGl57TqvL3qcJl33s=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\Reader-industry\\Reader-industry\\wwwroot\\lib\\jquery-validation-unobtrusive\\dist\\jquery.validate.unobtrusive.min.js", "SourceId": "Reader-industry", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\Reader-industry\\Reader-industry\\wwwroot\\", "BasePath": "_content/Reader-industry", "RelativePath": "lib/jquery-validation-unobtrusive/dist/jquery.validate.unobtrusive.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "4v8eqarkd7", "Integrity": "YJa7W8EiQdQpkk93iGEjjnLSUWRpRJbSfzfURh1kxz4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation-unobtrusive\\dist\\jquery.validate.unobtrusive.min.js", "FileLength": 5824, "LastWriteTime": "2025-07-09T14:58:03.5844538+00:00"}, "z1IehXMCDljGhyZcZLpKag67432dqms7DuRJ9WAfy9Y=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\Reader-industry\\Reader-industry\\wwwroot\\lib\\jquery-validation-unobtrusive\\LICENSE.txt", "SourceId": "Reader-industry", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\Reader-industry\\Reader-industry\\wwwroot\\", "BasePath": "_content/Reader-industry", "RelativePath": "lib/jquery-validation-unobtrusive/LICENSE#[.{fingerprint}]?.txt", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "356vix0kms", "Integrity": "16aFlqtpsG9RyieKZUUUjkJpqTgcJtWXwT312I4Iz1s=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation-unobtrusive\\LICENSE.txt", "FileLength": 1139, "LastWriteTime": "2025-07-09T14:58:03.2093701+00:00"}, "cPRceijtPwnnlcEwXY78xCnjjXilFqB6XwoWlcBOlsI=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\Reader-industry\\Reader-industry\\wwwroot\\lib\\jquery-validation\\dist\\additional-methods.js", "SourceId": "Reader-industry", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\Reader-industry\\Reader-industry\\wwwroot\\", "BasePath": "_content/Reader-industry", "RelativePath": "lib/jquery-validation/dist/additional-methods#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "83jwlth58m", "Integrity": "XL6yOf4sfG2g15W8aB744T4ClbiDG4IMGl2mi0tbzu0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation\\dist\\additional-methods.js", "FileLength": 53033, "LastWriteTime": "2025-07-09T14:58:03.367083+00:00"}, "Q+RAKG5J5lZXgoQZtX0c5hbYe+M9F1MCw/lvdID+CBQ=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\Reader-industry\\Reader-industry\\wwwroot\\lib\\jquery-validation\\dist\\additional-methods.min.js", "SourceId": "Reader-industry", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\Reader-industry\\Reader-industry\\wwwroot\\", "BasePath": "_content/Reader-industry", "RelativePath": "lib/jquery-validation/dist/additional-methods.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "mrlpezrjn3", "Integrity": "jhvKRxZo6eW/PyCe+4rjBLzqesJlE8rnyQGEjk8l2k8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation\\dist\\additional-methods.min.js", "FileLength": 22125, "LastWriteTime": "2025-07-09T14:58:03.3773251+00:00"}, "VjWZ9BcjuQRaF05DIYh2Deo2u3Tv+O2bVBAYysGJqJA=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\Reader-industry\\Reader-industry\\wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.js", "SourceId": "Reader-industry", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\Reader-industry\\Reader-industry\\wwwroot\\", "BasePath": "_content/Reader-industry", "RelativePath": "lib/jquery-validation/dist/jquery.validate#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "lzl9nlhx6b", "Integrity": "kRL82372ur5YrVTjFWp9muao9yeU8AoLiJxSb5ekmHg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.js", "FileLength": 52536, "LastWriteTime": "2025-07-09T14:58:03.3793218+00:00"}, "b4cCSuBylJJTE+uoYoKqlKdjIJPyXeiEnx0+TO0tHok=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\Reader-industry\\Reader-industry\\wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.min.js", "SourceId": "Reader-industry", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\Reader-industry\\Reader-industry\\wwwroot\\", "BasePath": "_content/Reader-industry", "RelativePath": "lib/jquery-validation/dist/jquery.validate.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "ag7o75518u", "Integrity": "umbTaFxP31Fv6O1itpLS/3+v5fOAWDLOUzlmvOGaKV4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.min.js", "FileLength": 25308, "LastWriteTime": "2025-07-09T14:58:03.5693908+00:00"}, "vUau+he+Xvd86VS12eA1r0pzMW9HHrjwIgf6qbuhKxc=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\Reader-industry\\Reader-industry\\wwwroot\\lib\\jquery-validation\\LICENSE.md", "SourceId": "Reader-industry", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\Reader-industry\\Reader-industry\\wwwroot\\", "BasePath": "_content/Reader-industry", "RelativePath": "lib/jquery-validation/LICENSE#[.{fingerprint}]?.md", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "x0q3zqp4vz", "Integrity": "geHEkw/WGPdaHQMRq5HuNY9snliNzU/y2OW8ycnhGXw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation\\LICENSE.md", "FileLength": 1117, "LastWriteTime": "2025-07-09T14:58:03.2053774+00:00"}, "WN3hBBcSRWJ2y16WmnHXg+6qLTchkF6TENVKZn2pTMg=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\Reader-industry\\Reader-industry\\wwwroot\\lib\\jquery\\dist\\jquery.js", "SourceId": "Reader-industry", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\Reader-industry\\Reader-industry\\wwwroot\\", "BasePath": "_content/Reader-industry", "RelativePath": "lib/jquery/dist/jquery#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "0i3buxo5is", "Integrity": "eKhayi8LEQwp4NKxN+CfCh+3qOVUtJn3QNZ0TciWLP4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery\\dist\\jquery.js", "FileLength": 285314, "LastWriteTime": "2025-07-09T14:58:03.2341929+00:00"}, "X+Az8LghF4jtJckiF57wRpb9LREsXy41gjoZFVv0Zs4=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\Reader-industry\\Reader-industry\\wwwroot\\lib\\jquery\\dist\\jquery.min.js", "SourceId": "Reader-industry", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\Reader-industry\\Reader-industry\\wwwroot\\", "BasePath": "_content/Reader-industry", "RelativePath": "lib/jquery/dist/jquery.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "o1o13a6vjx", "Integrity": "/JqT3SQfawRcv/BIHPThkBvs0OEvtFFmqPF/lYI/Cxo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery\\dist\\jquery.min.js", "FileLength": 87533, "LastWriteTime": "2025-07-09T14:58:03.235196+00:00"}, "diq2U28lSU5q6zE4G4+8L/fhTsyQKGzVzfYzkaybUDY=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\Reader-industry\\Reader-industry\\wwwroot\\lib\\jquery\\dist\\jquery.min.map", "SourceId": "Reader-industry", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\Reader-industry\\Reader-industry\\wwwroot\\", "BasePath": "_content/Reader-industry", "RelativePath": "lib/jquery/dist/jquery.min#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "ttgo8qnofa", "Integrity": "z3TVHGLSmRiZiRMOu0I7MEU1Mv3ImI2OK3GxuRZagLg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery\\dist\\jquery.min.map", "FileLength": 134755, "LastWriteTime": "2025-07-09T14:58:03.2434028+00:00"}, "P+S3R3yPGrhYrOwlym7JT/gqrTtml6KeQiDWGYphT/M=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\Reader-industry\\Reader-industry\\wwwroot\\lib\\jquery\\dist\\jquery.slim.js", "SourceId": "Reader-industry", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\Reader-industry\\Reader-industry\\wwwroot\\", "BasePath": "_content/Reader-industry", "RelativePath": "lib/jquery/dist/jquery.slim#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "2z0ns9nrw6", "Integrity": "UgvvN8vBkgO0luPSUl2s8TIlOSYRoGFAX4jlCIm9Adc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery\\dist\\jquery.slim.js", "FileLength": 232015, "LastWriteTime": "2025-07-09T14:58:03.2458083+00:00"}, "hlBw/96XLsOabofwwRN168uhAEXfKUDwhqZT1oDtpwY=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\Reader-industry\\Reader-industry\\wwwroot\\lib\\jquery\\dist\\jquery.slim.min.js", "SourceId": "Reader-industry", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\Reader-industry\\Reader-industry\\wwwroot\\", "BasePath": "_content/Reader-industry", "RelativePath": "lib/jquery/dist/jquery.slim.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "muycvpuwrr", "Integrity": "kmHvs0B+OpCW5GVHUNjv9rOmY0IvSIRcf7zGUDTDQM8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery\\dist\\jquery.slim.min.js", "FileLength": 70264, "LastWriteTime": "2025-07-09T14:58:03.2488083+00:00"}, "vGB4tmY6Z3Hm9/lFVXSxQh+wnBjT4Tkoafi9kLpj3+o=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\Reader-industry\\Reader-industry\\wwwroot\\lib\\jquery\\dist\\jquery.slim.min.map", "SourceId": "Reader-industry", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\Reader-industry\\Reader-industry\\wwwroot\\", "BasePath": "_content/Reader-industry", "RelativePath": "lib/jquery/dist/jquery.slim.min#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "87fc7y1x7t", "Integrity": "9FYmcgtx8qZo1OPPiPt/BJ/sz0EI8SRNUYsFLZDEEt4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery\\dist\\jquery.slim.min.map", "FileLength": 107143, "LastWriteTime": "2025-07-09T14:58:03.2548048+00:00"}, "A92n7YBsyG9Wx9NleYMQQndL3K7zY3FYECMt9CAeKVs=": {"Identity": "C:\\Users\\<USER>\\source\\repos\\Reader-industry\\Reader-industry\\wwwroot\\lib\\jquery\\LICENSE.txt", "SourceId": "Reader-industry", "SourceType": "Discovered", "ContentRoot": "C:\\Users\\<USER>\\source\\repos\\Reader-industry\\Reader-industry\\wwwroot\\", "BasePath": "_content/Reader-industry", "RelativePath": "lib/jquery/LICENSE#[.{fingerprint}]?.txt", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "mlv21k5csn", "Integrity": "hjIBkvmgxQXbNXK3B9YQ3t06RwLuQSQzC/dpvuB/lMk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery\\LICENSE.txt", "FileLength": 1117, "LastWriteTime": "2025-07-09T14:58:03.2033723+00:00"}}, "CachedCopyCandidates": {}}